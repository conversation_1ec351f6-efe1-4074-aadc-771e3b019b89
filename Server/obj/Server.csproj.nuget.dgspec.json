{"format": 1, "restore": {"/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Server.csproj": {}}, "projects": {"/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Server.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Server.csproj", "projectName": "Server", "projectPath": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Server.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AWSSDK.S3": {"target": "Package", "version": "[*******, )"}, "BCrypt.Net-Next": {"target": "Package", "version": "[4.0.3, )"}, "FluentValidation.AspNetCore": {"target": "Package", "version": "[11.3.1, )"}, "Microsoft.AspNetCore.Authentication.Cookies": {"target": "Package", "version": "[2.2.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/lib/dotnet/sdk/8.0.118/PortableRuntimeIdentifierGraph.json"}}}}}