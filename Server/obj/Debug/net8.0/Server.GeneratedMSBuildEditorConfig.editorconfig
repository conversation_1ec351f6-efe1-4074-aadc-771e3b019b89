is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = Server
build_property.RootNamespace = Server
build_property.ProjectDir = /home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = /home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server
build_property._RazorSourceGeneratorDebug = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Auth/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQXV0aC9Mb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Auth/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQXV0aC9SZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Blog/Create.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQmxvZy9DcmVhdGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Blog/Edit.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQmxvZy9FZGl0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Blog/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvQmxvZy9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSG9tZS9JbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvSG9tZS9Qcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL0Vycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL19WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3MvU2hhcmVkL19MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-m538voh91j
