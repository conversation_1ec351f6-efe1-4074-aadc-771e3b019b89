{"Files": [{"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/projectbundle/Server.bundle.scp.css", "PackagePath": "staticwebassets/Server.bundle.scp.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/css/site.css", "PackagePath": "staticwebassets/css/site.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/favicon.ico", "PackagePath": "staticwebassets/favicon.ico"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/js/site.js", "PackagePath": "staticwebassets/js/site.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/LICENSE", "PackagePath": "staticwebassets/lib/bootstrap"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "PackagePath": "staticwebassets/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "PackagePath": "staticwebassets/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "PackagePath": "staticwebassets/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "PackagePath": "staticwebassets/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/LICENSE.md", "PackagePath": "staticwebassets/lib/jquery-validation/LICENSE.md"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/additional-methods.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/additional-methods.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/additional-methods.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/jquery.validate.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "PackagePath": "staticwebassets/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/LICENSE.txt", "PackagePath": "staticwebassets/lib/jquery/LICENSE.txt"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/dist/jquery.js", "PackagePath": "staticwebassets/lib/jquery/dist/jquery.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/dist/jquery.min.js", "PackagePath": "staticwebassets/lib/jquery/dist/jquery.min.js"}, {"Id": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/dist/jquery.min.map", "PackagePath": "staticwebassets/lib/jquery/dist/jquery.min.map"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.Server.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.build.Server.props", "PackagePath": "build\\Server.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildMultiTargeting.Server.props", "PackagePath": "buildMultiTargeting\\Server.props"}, {"Id": "obj/Debug/net8.0/staticwebassets/msbuild.buildTransitive.Server.props", "PackagePath": "buildTransitive\\Server.props"}], "ElementsToRemove": []}