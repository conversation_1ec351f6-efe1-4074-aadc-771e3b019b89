/* _content/Server/Views/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-m538voh91j] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-m538voh91j] {
  color: #0077cc;
}

.btn-primary[b-m538voh91j] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-m538voh91j], .nav-pills .show > .nav-link[b-m538voh91j] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-m538voh91j] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-m538voh91j] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-m538voh91j] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-m538voh91j] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-m538voh91j] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
