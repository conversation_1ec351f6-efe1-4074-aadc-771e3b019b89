{"Version": 1, "Hash": "H6r8lsv9I2CkSRu0bh4N7gYGK5WYgfSmDemaoUjfx8c=", "Source": "Server", "BasePath": "_content/Server", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "Server/wwwroot", "Source": "Server", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "Pattern": "**"}], "Assets": [{"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/bundle/Server.styles.css", "SourceId": "Server", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/bundle/", "BasePath": "_content/Server", "RelativePath": "Server.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/bundle/Server.styles.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/projectbundle/Server.bundle.scp.css", "SourceId": "Server", "SourceType": "Computed", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/projectbundle/", "BasePath": "_content/Server", "RelativePath": "Server.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/obj/Debug/net8.0/scopedcss/projectbundle/Server.bundle.scp.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/css/site.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/css/site.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/favicon.ico", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/favicon.ico"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/js/site.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/js/site.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.js.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.js.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/bootstrap/LICENSE", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/bootstrap/LICENSE", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/bootstrap/LICENSE"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/additional-methods.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation/dist/additional-methods.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/additional-methods.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation/dist/additional-methods.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/additional-methods.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/jquery.validate.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation/dist/jquery.validate.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/dist/jquery.validate.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/dist/jquery.validate.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery-validation/LICENSE.md", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery-validation/LICENSE.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery-validation/LICENSE.md"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/dist/jquery.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery/dist/jquery.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/dist/jquery.min.js", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery/dist/jquery.min.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.js"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/dist/jquery.min.map", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery/dist/jquery.min.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/dist/jquery.min.map"}, {"Identity": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/lib/jquery/LICENSE.txt", "SourceId": "Server", "SourceType": "Discovered", "ContentRoot": "/home/<USER>/Documents/coding projects/ASP.NET/QuipsAndQuills/Server/wwwroot/", "BasePath": "_content/Server", "RelativePath": "lib/jquery/LICENSE.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot/lib/jquery/LICENSE.txt"}]}