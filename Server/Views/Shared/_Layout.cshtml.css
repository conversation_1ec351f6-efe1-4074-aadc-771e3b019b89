/* ===== QUIPS & QUILLS - LAYOUT STYLES ===== */
/* Clean, professional design system matching App.css */

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Brand Colors */
  --brand-primary: #2563eb;
  --brand-primary-hover: #1d4ed8;
  --brand-primary-light: #dbeafe;
  --brand-secondary: #64748b;

  /* Neutral Palette */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Spacing */
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-6: 1.5rem;

  /* Border Radius */
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
}

/* ===== NAVBAR STYLES ===== */
.navbar-brand {
  font-weight: 700;
  font-size: 1.25rem;
  color: var(--brand-primary) !important;
  transition: color var(--transition-fast);
  white-space: nowrap;
  text-align: left;
  word-break: normal;
}

.navbar-brand:hover {
  color: var(--brand-primary-hover) !important;
  text-decoration: none;
}

/* ===== LINK STYLES ===== */
a {
  color: var(--brand-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--brand-primary-hover);
  text-decoration: underline;
}

/* ===== BUTTON STYLES ===== */
.btn-primary {
  color: #ffffff;
  background-color: var(--brand-primary);
  border-color: var(--brand-primary);
  font-weight: 500;
  padding: var(--space-3) var(--space-4);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  background-color: var(--brand-primary-hover);
  border-color: var(--brand-primary-hover);
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:focus {
  box-shadow: 0 0 0 3px var(--brand-primary-light);
}

/* ===== NAVIGATION PILLS ===== */
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #ffffff;
  background-color: var(--brand-primary);
  border-color: var(--brand-primary);
  font-weight: 500;
}

.nav-pills .nav-link {
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.nav-pills .nav-link:hover {
  background-color: var(--brand-primary-light);
  color: var(--brand-primary);
}

/* ===== BORDER UTILITIES ===== */
.border-top {
  border-top: 1px solid var(--gray-200) !important;
}

.border-bottom {
  border-bottom: 1px solid var(--gray-200) !important;
}

/* ===== SHADOW UTILITIES ===== */
.box-shadow {
  box-shadow: var(--shadow-sm);
}

/* ===== FORM STYLES ===== */
.form-control:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px var(--brand-primary-light);
}

.form-check-input:focus {
  border-color: var(--brand-primary);
  box-shadow: 0 0 0 3px var(--brand-primary-light);
}

/* ===== ALERT STYLES ===== */
.alert {
  border-radius: var(--radius-lg);
  border: none;
  font-weight: 500;
}

.alert-success {
  background-color: #dcfce7;
  color: #166534;
}

.alert-danger {
  background-color: #fef2f2;
  color: #991b1b;
}

.alert-info {
  background-color: #e0f2fe;
  color: #0c4a6e;
}

.alert-warning {
  background-color: #fef3c7;
  color: #92400e;
}

/* ===== CARD STYLES ===== */
.card {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.card-header {
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  font-weight: 600;
}

/* ===== FOOTER STYLES ===== */
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  padding: var(--space-4) 0;
  color: var(--gray-500);
  font-size: 0.875rem;
}

.footer a {
  color: var(--gray-600);
}

.footer a:hover {
  color: var(--brand-primary);
}

/* ===== POLICY BUTTON ===== */
button.accept-policy {
  font-size: 1rem;
  line-height: inherit;
  background-color: var(--brand-primary);
  color: #ffffff;
  border: none;
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-4);
  font-weight: 500;
  transition: all var(--transition-fast);
}

button.accept-policy:hover {
  background-color: var(--brand-primary-hover);
  transform: translateY(-1px);
}
