@{
    ViewData["Title"] = "Register";
}

if (User.Identity != null && User.Identity.IsAuthenticated)
{
    <div class="alert alert-info">
        <h4>You are already logged in as @User.Identity.Name.</h4>
        <p>If you want to create a new account, please <a asp-action="LogoutGet">log out</a> first.</p>
    </div>
}
else
{
    <div class="container mt-5">
        <h1 class="mb-4">Create Your Account</h1>

        <form asp-action="Register" method="post">
            @* Display error messages if any. *@
            @if (TempData["ErrorMessage"] != null)
            {
                <div class="alert alert-danger">
                    @TempData["ErrorMessage"]
                </div>
            }

            <div class="mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" required />
            </div>

            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" required />
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required />
            </div>
            <div class="mb-3">
                <label for="confirmPassword" class="form-label">Confirm Password</label>
                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required />
            </div>

            <button type="submit" class="btn btn-primary">Register</button>
        </form>

        <p class="mt-3"></p>
            Already have an account? <a asp-action="Login">Sign In</a>
        </p>
    </div>
}