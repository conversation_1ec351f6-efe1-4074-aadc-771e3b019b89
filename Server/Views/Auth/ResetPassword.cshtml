@model ResetPasswordViewModel
@{
    ViewData["Title"] = "Reset Password";
}

<div class="container mt-5">
    <h1>Reset Password</h1>

    @* Display error messages if any. *@
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success">
            @TempData["SuccessMessage"]
        </div>
    }
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <form asp-action="ResetPassword" method="post">
        @* Hidden field to pass the token *@
        <input type="hidden" asp-for="Token" />

        <div class="mb-3">
            <label asp-for="NewPassword" class="form-label">New Password</label>
            <input asp-for="NewPassword" type="password" class="form-control" required />
            <span asp-validation-for="NewPassword" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="ConfirmPassword" class="form-label">Confirm Password</label>
            <input asp-for="ConfirmPassword" type="password" class="form-control" required />
            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
        </div>

        <button type="submit" class="btn btn-primary">Reset Password</button>
    </form>

    <p class="mt-3">
        Remembered your password? <a asp-action="Login">Back to Login</a>
    </p>
</div>