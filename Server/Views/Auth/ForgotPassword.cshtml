@{
    ViewData["Title"] = "Forgot Password";
}

<div class="container mt-5">
    <h1 class="mb-4">Forgot Password</h1>

    @* Display error messages if any. *@
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success">
            @TempData["SuccessMessage"]
        </div>
    }
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <form asp-action="ForgotPassword" method="post">
        <div class="mb-3">
            <label for="email" class="form-label">Email Address</label>
            <input type="email" class="form-control" id="email" name="email" required />
        </div>

        <button type="submit" class="btn btn-primary">Send Reset Link</button>
    </form>

    <p class="mt-3">
        Remembered your password? <a asp-action="Login">Back to Login</a>
    </p>
</div>