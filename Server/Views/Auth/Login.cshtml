@{
    ViewData["Title"] = "Login";
}

@* If the user is already logged in, display a message and a link to log out. *@
@if (User.Identity != null && User.Identity.IsAuthenticated)
{
    <div class="alert alert-info">
        <h4>You are already logged in as @User.Identity.Name.</h4>
        <p>If you want to log in as a different user, please <a asp-action="LogoutGet">log out</a> first.</p>
    </div>
}
else
{
    <div class="container mt-5">
        @* Display error messages if any. *@
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger">
                @TempData["ErrorMessage"]
            </div>
        }
        @if (!ViewData.ModelState.IsValid)
        {
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                    {
                        <li>@error.ErrorMessage</li>
                    }
                </ul>
            </div>
        }

        <h1 class="mb-4">Sign In</h1>

        <form asp-action="Login" method="post">
            <div class="mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" required />
            </div>

            <div class="mb-3">
                <label for="password" class="form-label">Password</label>
                <input type="password" class="form-control" id="password" name="password" required />
            </div>

            <button type="submit" class="btn btn-primary">Login</button>
        </form>

        <p class="mt-3">
            Don't have an account? <a asp-action="Register">Create one</a>
        </p>
        <p>
            <a asp-action="ForgotPassword">Forgot your password?</a>
        </p>
    </div>
}