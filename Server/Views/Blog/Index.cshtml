@model List<Server.Models.BlogPost>
@{
    ViewData["Title"] = "Blog Feed";
}

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Latest Posts</h1>
    <a asp-action="Create" asp-controller="Blog" class="btn btn-primary">
        <i class="bi bi-plus"></i> Write New Post
    </a>
</div>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (Model.Any())
{
    <div class="row">
        @foreach (var post in Model)
        {
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <small class="card-text">
                            <a asp-action="Profile" asp-route-id="@post.UserId">
                                @post.User.Username
                            </a>
                        </small>
                        <h5 class="card-title">@post.Title</h5>
                        <p class="card-text">@post.Excerpt</p>
                        <div class="d-flex justify-content-between align-items-center">
                            @if (User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value == post.UserId.ToString())
                            {
                                <div class="btn-group btn-group-sm" role="group">
                                    <a asp-action="Edit" asp-controller="Blog" asp-route-id="@post.Id" 
                                       class="btn btn-outline-secondary">Edit</a>
                                    <form method="post" asp-action="Delete" asp-controller="Blog" asp-route-id="@post.Id" 
                                          class="d-inline" onsubmit="return confirm('Are you sure you want to delete this post?')">
                                        @Html.AntiForgeryToken()
                                        <button type="submit" class="btn btn-outline-danger">Delete</button>
                                    </form>
                                </div>
                            }
                            @* Created At (hh:mm '•' MMM, d, yyyy) *@
                            @post.CreatedAt.ToString("hh:mm '•' MMM, d, yyyy")
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
}
else
{
    <div class="text-center py-5">
        <h3>No posts yet</h3>
        <p class="text-muted">Be the first to share your thoughts!</p>
        <a asp-action="Create" asp-controller="Blog" class="btn btn-primary">Write Your First Post</a>
    </div>
}
