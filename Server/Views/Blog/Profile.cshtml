@* Page to view profiles, be they yours or others *@
@{
    ViewData["Title"] = "User Profile";
}

<div class="container mt-5">
    <h1 class="mb-4">User Profile</h1>

    @* Display error messages if any. *@
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success">
            @TempData["SuccessMessage"]
        </div>
    }
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <div>
        <h2>@User.Identity.Name's Profile</h2>
        @* Additional profile details can be added here *@ 
        <p>
            <strong>Bio:</strong> @User.FindFirst("Bio")?.Value ?? "No bio available."
        </p>
        <p>
            <strong>Joined:</strong> @User.CreatedAt ?? "Unknown"
        </p>
        <p>
            <strong>Posts:</strong> @User.BlogPosts.Count() ?? 0

    </div>
</div>