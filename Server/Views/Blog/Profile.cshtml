@model Server.Models.User
@* Page to view profiles, be they yours or others *@
@{
    ViewData["Title"] = $"{Model.FirstName} {Model.LastName} - Profile";
}

<div class="container mt-5">
    <h1 class="mb-4">User Profile</h1>

    @* Display error messages if any. *@
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger">
            @TempData["ErrorMessage"]
        </div>
    }
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success">
            @TempData["SuccessMessage"]
        </div>
    }
    @if (!ViewData.ModelState.IsValid)
    {
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                {
                    <li>@error.ErrorMessage</li>
                }
            </ul>
        </div>
    }

    <div class="row">
        <div class="col-md-4">
            @* Profile Image *@
            <div class="text-center mb-4">
                @if (!string.IsNullOrEmpty(Model.ProfileImageUrl))
                {
                    <img src="@Model.ProfileImageUrl" alt="Profile Picture" class="rounded-circle img-fluid" style="max-width: 200px; max-height: 200px;">
                }
                else
                {
                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 200px; height: 200px; margin: 0 auto;">
                        <i class="fas fa-user fa-5x text-white"></i>
                    </div>
                }
            </div>
        </div>

        <div class="col-md-8">
            @* Profile Information *@
            <h2>@Model.FirstName @Model.LastName</h2>
            <p class="text-muted">@@@Model.Username</p>

            <div class="mb-3">
                <strong>Bio:</strong>
                <p>@(string.IsNullOrEmpty(Model.Bio) ? "No bio available." : Model.Bio)</p>
            </div>

            <div class="row">
                <div class="col-sm-6">
                    <p><strong>Email:</strong> @Model.Email</p>
                    <p><strong>Joined:</strong> @Model.CreatedAt.ToString("MMMM dd, yyyy")</p>
                    <p><strong>Last Updated:</strong> @Model.UpdatedAt.ToString("MMMM dd, yyyy")</p>
                </div>
                <div class="col-sm-6">
                    @if (Model.LastLoginAt.HasValue)
                    {
                        <p><strong>Last Login:</strong> @Model.LastLoginAt.Value.ToString("MMMM dd, yyyy")</p>
                    }
                    <p><strong>Account Status:</strong>
                        <span class="badge @(Model.IsActive ? "bg-success" : "bg-danger")">
                            @(Model.IsActive ? "Active" : "Inactive")
                        </span>
                    </p>
                    <p><strong>Email Verified:</strong>
                        <span class="badge @(Model.EmailVerified ? "bg-success" : "bg-warning")">
                            @(Model.EmailVerified ? "Verified" : "Unverified")
                        </span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    @* Blog Posts Section *@
    <div class="mt-5">
        <h3>Blog Posts</h3>
        <p><strong>Total Posts:</strong> @Model.BlogPosts.Count</p>

        @if (Model.BlogPosts.Any())
        {
            <div class="row">
                @foreach (var post in Model.BlogPosts.Take(6))
                {
                    <div class="col-md-6 col-lg-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">@post.Title</h5>
                                <p class="card-text">@post.Excerpt</p>
                                <p class="card-text">
                                    <small class="text-muted">
                                        @post.CreatedAt.ToString("MMM dd, yyyy")
                                        @if (post.IsPublished)
                                        {
                                            <span class="badge bg-success ms-2">Published</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary ms-2">Draft</span>
                                        }
                                    </small>
                                </p>
                                <a href="#" class="btn btn-primary btn-sm">Read More</a>
                            </div>
                        </div>
                    </div>
                }
            </div>

            @if (Model.BlogPosts.Count > 6)
            {
                <div class="text-center mt-3">
                    <a href="#" class="btn btn-outline-primary">View All Posts (@Model.BlogPosts.Count)</a>
                </div>
            }
        }
        else
        {
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> This user hasn't written any blog posts yet.
            </div>
        }
    </div>
</div>