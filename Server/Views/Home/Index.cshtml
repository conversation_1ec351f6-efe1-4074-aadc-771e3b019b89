﻿@{
    ViewData["Title"] = "Home Page";
}

<div class="text-center">
    <h1 class="display-4">Quips and Quills</h1>
    <p class="lead">Welcome to your personal blog platform!</p>
    
    @if (User.Identity != null && User.Identity.IsAuthenticated)
    {
        <div class="mt-4">
            <h3>Welcome back, @User.Identity.Name!</h3>
            <p>Ready to share your thoughts with the world?</p>
            <a asp-controller="Blog" asp-action="Index" class="btn btn-primary btn-lg me-3">View Blog Feed</a>
            <a asp-controller="Blog" asp-action="Create" class="btn btn-outline-secondary btn-lg">Write New Post</a>
        </div>
    }
    else
    {
        <div class="mt-4">
            <p>Join our community of writers and share your stories with the world.</p>
            <a asp-controller="Auth" asp-action="Register" class="btn btn-success btn-lg me-3">Get Started</a>
            <a asp-controller="Auth" asp-action="Login" class="btn btn-outline-primary btn-lg">Sign In</a>
        </div>
    }
</div>
