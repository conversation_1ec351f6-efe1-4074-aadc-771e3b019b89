                                               Table "public.Users"
         Column         |           Type           | Collation | Nullable |                Default                
------------------------+--------------------------+-----------+----------+---------------------------------------
 Id                     | integer                  |           | not null | generated by default as identity
 Username               | character varying(50)    |           | not null | 
 Email                  | character varying(100)   |           | not null | 
 PasswordHash           | character varying(255)   |           | not null | 
 CreatedAt              | timestamp with time zone |           | not null | CURRENT_TIMESTAMP
 Bio                    | character varying(500)   |           | not null | ''::character varying
 EmailVerificationToken | character varying(255)   |           |          | 
 EmailVerified          | boolean                  |           | not null | false
 FirstName              | character varying(100)   |           | not null | ''::character varying
 IsActive               | boolean                  |           | not null | false
 LastLoginAt            | timestamp with time zone |           |          | 
 LastName               | character varying(100)   |           | not null | ''::character varying
 PasswordResetExpires   | timestamp with time zone |           |          | 
 PasswordResetToken     | character varying(255)   |           |          | 
 ProfileImageUrl        | character varying(255)   |           |          | 
 UpdatedAt              | timestamp with time zone |           | not null | '-infinity'::timestamp with time zone
Indexes:
    "PK_Users" PRIMARY KEY, btree ("Id")
    "IX_Users_Email" UNIQUE, btree ("Email")
    "IX_Users_Username" UNIQUE, btree ("Username")
Referenced by:
    TABLE ""BlogPosts"" CONSTRAINT "FK_BlogPosts_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users"("Id") ON DELETE CASCADE
    TABLE ""PasswordResetTokens"" CONSTRAINT "FK_PasswordResetTokens_Users_UserId" FOREIGN KEY ("UserId") REFERENCES "Users"("Id") ON DELETE CASCADE

