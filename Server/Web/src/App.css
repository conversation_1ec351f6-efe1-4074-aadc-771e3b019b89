/* App.css */
/* CSS variables for background colors */

:root {
  --color-background: #f0f0f0;
  --color-background-light: #ffffff;
  --color-background-dark: #2e2f31;
  --color-text: #333333;
  --color-text-light: #666666;
  --color-primary: #5c98d7;
  --color-primary-light: #5c98d7;
  --color-primary-dark: #2c5785;
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
