using System.ComponentModel.DataAnnotations;

namespace Server.Models;

public class User
{
    public int Id { get; set; }

    [Required]
    [StringLength(50)]
    public string Username { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string PasswordHash { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string Bio { get; set; } = string.Empty;

    [StringLength(255)]
    public string? ProfileImageUrl { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? LastLoginAt { get; set; }

    public bool IsActive { get; set; } = true;
    public bool EmailVerified { get; set; } = false;

    [StringLength(255)]
    public string? EmailVerificationToken { get; set; }

    [StringLength(255)]
    public string? PasswordResetToken { get; set; }

    public DateTime? PasswordResetExpires { get; set; }

    // Navigation property for blog posts
    public virtual ICollection<BlogPost> BlogPosts { get; set; } = new List<BlogPost>();
}