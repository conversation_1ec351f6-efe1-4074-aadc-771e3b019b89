// 1. IMPORTS/USING STATEMENTS
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using System.Security.Claims;
using Server.Models;
using QuipsAndQuills.Server.Models;
using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using System.Text;

// 2. NAMESPACE AND CLASS DECLARATION
namespace Server.Controllers
{
    // This should be a CONTROLLER (not ApiController) since we're using MVC views
    public class AuthController : Controller
    {
        // 3. DEPENDENCY INJECTION FIELDS
        private readonly ApplicationDbContext _context;

        // 4. CONSTRUCTOR
        public AuthController(ApplicationDbContext context)
        {
            // here are each of the dependencies needed for this controller to function
            _context = context; // context is needed for database access
        }

        // 5. LOGIN GET ACTION (Show login form)
        [HttpGet]
        public IActionResult Login()
        {
            // Return the login view
            return View();
        }

        // 6. LOGIN POST ACTION (Process login)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Login(string email, string password, bool rememberMe = false)
        {
            // Pseudocode steps:
            // IF input is invalid
            //     Add error to ModelState
            //     Return view with errors
            // 
            // VALIDATE input (required fields, email format)
            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
            {
                ModelState.AddModelError(string.Empty, "Email and password are required.");
                return View();
            }

            if (!IsValidEmail(email))
            {
                ModelState.AddModelError(string.Empty, "Invalid email format.");
                return View();
            }

            // FIND user by email in database
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == email);
            if (user == null)
            {
                ModelState.AddModelError(string.Empty, "User not found.");
                return View();
            }


            // IF password doesn't match hash
            //     Add error message  
            //     Return view with error
            //

            if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
            {
                // modelstate is used to pass validation errors back to the view
                // addmodelerror is used to add an error message to the model state
                // string.empty is used as the key for the error message
                ModelState.AddModelError(string.Empty, "Invalid password.");
                return View();
            }

            // CREATE claims list with user info
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new Claim(ClaimTypes.Name, user.Username),
                new Claim(ClaimTypes.Email, user.Email)
            };

            // CREATE claims identity with cookie scheme
            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);

            // Set authentication properties based on rememberMe
            var authProperties = new AuthenticationProperties
            {
                IsPersistent = rememberMe,
                ExpiresUtc = rememberMe ? DateTimeOffset.UtcNow.AddDays(7) : null
            };

            // SIGN IN user with claims
            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme,
                new ClaimsPrincipal(claimsIdentity), authProperties);

            // REDIRECT to blog index or return URL
            TempData["SuccessMessage"] = "Welcome back, " + user.Username + "!";
            return RedirectToAction("Index", "Blog");
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        // 7. REGISTER GET ACTION (Show registration form)
        [HttpGet]
        public IActionResult Register()
        {
            // Return the register view
            return View();
        }

        // 8. REGISTER POST ACTION (Process registration)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Register(string username, string email, string password, string confirmPassword)
        {
            // Pseudocode steps:
            // VALIDATE input (required fields, email format, password length, passwords match)
            // IF validation fails
            //     Add errors to ModelState
            //     Return view with errors
            //
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password) || string.IsNullOrEmpty(confirmPassword))
            {
                ModelState.AddModelError(string.Empty, "All fields are required.");
                return View();
            }

            if (!IsValidEmail(email))
            {
                ModelState.AddModelError(string.Empty, "Invalid email format.");
                return View();
            }

            if (password.Length < 6)
            {
                ModelState.AddModelError(string.Empty, "Password must be at least 6 characters long.");
                return View();
            }

            if (password != confirmPassword)
            {
                ModelState.AddModelError(string.Empty, "Passwords do not match.");
                return View();
            }

            // CHECK if username or email already exists
            // IF exists
            //     Add error message
            //     Return view with error
            //
            if (await _context.Users.AnyAsync(u => u.Username == username || u.Email == email))
            {
                ModelState.AddModelError(string.Empty, "Username or email already exists.");
                return View();
            }

            // HASH the password
            string passwordHash = BCrypt.Net.BCrypt.HashPassword(password);
            // CREATE new User object
            var newUser = new User
            {
                Username = username,
                Email = email.ToLowerInvariant(),
                PasswordHash = passwordHash,
                CreatedAt = DateTime.UtcNow
            };
            // ADD user to database
            _context.Users.Add(newUser);
            // SAVE changes
            await _context.SaveChangesAsync();
            // SET success message
            TempData["SuccessMessage"] = "Registration successful. Please log in.";
            // Log user in automatically
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.NameIdentifier, newUser.Id.ToString()),
                new Claim(ClaimTypes.Name, newUser.Username),
                new Claim(ClaimTypes.Email, newUser.Email)
            };
            var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
            var authProperties = new AuthenticationProperties
            {
                IsPersistent = true, // Remember me
                ExpiresUtc = DateTimeOffset.UtcNow.AddDays(30) // Set cookie expiration
            };
            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);
            // REDIRECT to blog index or return URL
            return RedirectToAction("Index", "Blog");
        }

        // 9. LOGOUT POST ACTION
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Logout()
        {
            // SIGN OUT user
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            // REDIRECT to home page
            return RedirectToAction("Index", "Home");
        }

        // 10. FORGOT PASSWORD GET ACTION (Show forgot password form)
        [HttpGet]
        public IActionResult ForgotPassword()
        {
            // Return the forgot password view
            return View();
        }

        // 11. FORGOT PASSWORD POST ACTION (Process forgot password)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ForgotPassword(string email)
        {
            // Pseudocode steps:
            // VALIDATE input (required field, email format)
            // IF validation fails
            //     Add error to ModelState
            //     Return view with error
            if (string.IsNullOrEmpty(email) || !IsValidEmail(email))
            {
                ModelState.AddModelError(string.Empty, "Please enter a valid email address.");
                return View();
            }

            // FIND user by email
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Email == email.ToLowerInvariant());
            // IF user not found
            //     Add error message
            //     Return view with error
            if (user == null)
            {
                ModelState.AddModelError(string.Empty, "No user found with that email address.");
                return View();
            }
            // GENERATE password reset token
            var token = Guid.NewGuid().ToString();
            // SAVE token to database (you might want to create a PasswordResetToken model)
            var resetToken = new PasswordResetToken
            {
                UserId = user.Id,
                Token = token,
                CreatedAt = DateTime.UtcNow,
                ExpiresAt = DateTime.UtcNow.AddHours(1) // Token valid for 1 hour
            };
            _context.PasswordResetTokens.Add(resetToken);
            await _context.SaveChangesAsync();

            TempData["LinkSentMsg"] = "Password reset link has been sent. Check your email.";
            return RedirectToAction("Login");
        }
    }
}