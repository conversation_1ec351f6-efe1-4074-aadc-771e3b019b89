using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Server.Models;
using System.Security.Claims;

namespace Server.Controllers;

[Authorize] // Require authentication for all blog actions
public class BlogController : Controller
{
    private readonly ApplicationDbContext _context;

    public BlogController(ApplicationDbContext context)
    {
        _context = context;
    }

    // GET: Blog (Feed page - shows recent posts)
    public async Task<IActionResult> Index()
    {
        var posts = await _context.BlogPosts
            .Include(p => p.User)
            .Where(p => p.IsPublished)
            .OrderByDescending(p => p.CreatedAt)
            .Take(20)
            .ToListAsync();

        return View(posts);
    }

    // GET: Blog/Create
    public IActionResult Create()
    {
        return View();
    }

    // POST: Blog/Create
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(string title, string content, string excerpt = "")
    {
        if (string.IsNullOrEmpty(title) || string.IsNullOrEmpty(content))
        {
            ModelState.AddModelError(string.Empty, "Title and content are required.");
            ViewBag.Title = title;
            ViewBag.Content = content;
            ViewBag.Excerpt = excerpt;
            return View();
        }

        var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);

        // Auto-generate excerpt if not provided
        if (string.IsNullOrEmpty(excerpt))
        {
            excerpt = content.Length > 200 ? content.Substring(0, 200) + "..." : content;
        }

        var blogPost = new BlogPost
        {
            Title = title,
            Content = content,
            Excerpt = excerpt,
            UserId = currentUserId,
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            IsPublished = true
        };

        _context.BlogPosts.Add(blogPost);
        await _context.SaveChangesAsync();

        TempData["SuccessMessage"] = "Your blog post has been published!";
        return RedirectToAction(nameof(Index));
    }

    // GET: Blog/Edit/5
    public async Task<IActionResult> Edit(int id)
    {
        var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);
        
        var blogPost = await _context.BlogPosts
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == currentUserId);

        if (blogPost == null)
        {
            return NotFound();
        }

        return View(blogPost);
    }

    // POST: Blog/Edit/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(int id, string title, string content, string excerpt = "")
    {
        if (string.IsNullOrEmpty(title) || string.IsNullOrEmpty(content))
        {
            ModelState.AddModelError(string.Empty, "Title and content are required.");
            var post = await _context.BlogPosts.FindAsync(id);
            return View(post);
        }

        var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);
        
        var blogPost = await _context.BlogPosts
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == currentUserId);

        if (blogPost == null)
        {
            return NotFound();
        }

        // Auto-generate excerpt if not provided
        if (string.IsNullOrEmpty(excerpt))
        {
            excerpt = content.Length > 200 ? content.Substring(0, 200) + "..." : content;
        }

        blogPost.Title = title;
        blogPost.Content = content;
        blogPost.Excerpt = excerpt;
        blogPost.UpdatedAt = DateTime.UtcNow;

        _context.Update(blogPost);
        await _context.SaveChangesAsync();

        TempData["SuccessMessage"] = "Your blog post has been updated!";
        return RedirectToAction(nameof(Index));
    }

    // POST: Blog/Delete/5
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        var currentUserId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)!.Value);
        
        var blogPost = await _context.BlogPosts
            .FirstOrDefaultAsync(p => p.Id == id && p.UserId == currentUserId);

        if (blogPost == null)
        {
            return NotFound();
        }

        _context.BlogPosts.Remove(blogPost);
        await _context.SaveChangesAsync();

        TempData["SuccessMessage"] = "Your blog post has been deleted.";
        return RedirectToAction(nameof(Index));
    }
}
