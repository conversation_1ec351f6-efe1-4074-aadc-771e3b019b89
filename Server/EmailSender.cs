using System.Net.Mail;
using System.Net;

namespace SendingEmails
{
    public class EmailSender : IEmailSender
    {
        // This email and password are the accurate credentials for the email account.
        private readonly string _mail = "<EMAIL>";
        private readonly string _password = "xihn ceqw pytl nhxv";

        public async Task SendEmailAsync(string email, string subject, string message)
        {
            var client = new SmtpClient("smtp.gmail.com", 587)
            {
                EnableSsl = true,
                Credentials = new NetworkCredential(_mail, _password)
            };

            await client.SendMailAsync(
                new MailMessage(
                    from: _mail,
                    to: email,
                    subject: subject,
                    body: message
                )
            );
        }
    }
}