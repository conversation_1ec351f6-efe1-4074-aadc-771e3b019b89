/* ===== QUIPS & QUILLS - PROFESSIONAL DESIGN SYSTEM ===== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css');

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
  /* Brand Colors */
  --brand-primary: #2563eb;
  --brand-primary-hover: #1d4ed8;
  --brand-primary-light: #dbeafe;
  --brand-secondary: #64748b;
  --brand-accent: #059669;

  /* Semantic Colors */
  --color-success: #059669;
  --color-warning: #d97706;
  --color-danger: #dc2626;
  --color-info: #0284c7;

  /* Neutral Palette */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;

  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;

  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===== BASE STYLES ===== */
html {
  font-size: 16px;
  position: relative;
  min-height: 100%;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--gray-700);
  background-color: var(--gray-50);
  margin-bottom: 80px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY IMPROVEMENTS ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
}

a {
  color: var(--brand-primary);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--brand-primary-hover);
  text-decoration: underline;
}

/* ===== ENHANCED BOOTSTRAP OVERRIDES ===== */
.container {
  max-width: 1200px;
}

/* Navigation Improvements */
.navbar {
  background-color: var(--gray-50) !important;
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  padding: var(--space-4) 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: var(--font-size-xl);
  color: var(--brand-primary) !important;
  transition: color var(--transition-fast);
}

.navbar-brand:hover {
  color: var(--brand-primary-hover) !important;
}

.nav-link {
  font-weight: 500;
  color: var(--gray-600) !important;
  padding: var(--space-2) var(--space-4) !important;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.nav-link:hover {
  color: var(--brand-primary) !important;
  background-color: var(--brand-primary-light);
}

.dropdown-menu {
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-lg);
  border-radius: var(--radius-lg);
  padding: var(--space-2);
}

.dropdown-item {
  border-radius: var(--radius-md);
  padding: var(--space-2) var(--space-3);
  transition: all var(--transition-fast);
}

.dropdown-item:hover {
  background-color: var(--brand-primary-light);
  color: var(--brand-primary);
}